<!DOCTYPE html>
<html>
<head>
    <title>生成PWA图标</title>
</head>
<body>
    <h2>PWA图标生成器</h2>
    <p>请将您的logo图片拖拽到下面的区域，会自动生成所需的PWA图标</p>
    
    <div id="drop-zone" style="border: 2px dashed #ccc; padding: 20px; text-align: center; margin: 20px 0;">
        拖拽图片到这里或点击选择文件
        <input type="file" id="file-input" accept="image/*" style="display: none;">
    </div>
    
    <div id="preview"></div>
    
    <script>
        const dropZone = document.getElementById('drop-zone');
        const fileInput = document.getElementById('file-input');
        const preview = document.getElementById('preview');
        
        const sizes = [72, 96, 128, 144, 152, 192, 384, 512];
        
        dropZone.addEventListener('click', () => fileInput.click());
        
        dropZone.addEventListener('dragover', (e) => {
            e.preventDefault();
            dropZone.style.backgroundColor = '#f0f0f0';
        });
        
        dropZone.addEventListener('dragleave', () => {
            dropZone.style.backgroundColor = '';
        });
        
        dropZone.addEventListener('drop', (e) => {
            e.preventDefault();
            dropZone.style.backgroundColor = '';
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                processFile(files[0]);
            }
        });
        
        fileInput.addEventListener('change', (e) => {
            if (e.target.files.length > 0) {
                processFile(e.target.files[0]);
            }
        });
        
        function processFile(file) {
            const reader = new FileReader();
            reader.onload = (e) => {
                const img = new Image();
                img.onload = () => {
                    generateIcons(img);
                };
                img.src = e.target.result;
            };
            reader.readAsDataURL(file);
        }
        
        function generateIcons(img) {
            preview.innerHTML = '<h3>生成的图标：</h3>';
            
            sizes.forEach(size => {
                const canvas = document.createElement('canvas');
                const ctx = canvas.getContext('2d');
                canvas.width = size;
                canvas.height = size;
                
                // 绘制圆角矩形背景
                ctx.fillStyle = '#824dfc';
                roundRect(ctx, 0, 0, size, size, size * 0.1);
                ctx.fill();
                
                // 计算图片位置（居中，留边距）
                const padding = size * 0.1;
                const imgSize = size - padding * 2;
                ctx.drawImage(img, padding, padding, imgSize, imgSize);
                
                // 创建下载链接
                const link = document.createElement('a');
                link.download = `icon-${size}x${size}.png`;
                link.href = canvas.toDataURL();
                link.textContent = `下载 ${size}x${size}`;
                link.style.display = 'inline-block';
                link.style.margin = '5px';
                link.style.padding = '5px 10px';
                link.style.backgroundColor = '#824dfc';
                link.style.color = 'white';
                link.style.textDecoration = 'none';
                link.style.borderRadius = '4px';
                
                preview.appendChild(link);
                
                // 显示预览
                const previewImg = document.createElement('img');
                previewImg.src = canvas.toDataURL();
                previewImg.style.width = '64px';
                previewImg.style.height = '64px';
                previewImg.style.margin = '5px';
                previewImg.style.border = '1px solid #ccc';
                preview.appendChild(previewImg);
            });
        }
        
        function roundRect(ctx, x, y, width, height, radius) {
            ctx.beginPath();
            ctx.moveTo(x + radius, y);
            ctx.lineTo(x + width - radius, y);
            ctx.quadraticCurveTo(x + width, y, x + width, y + radius);
            ctx.lineTo(x + width, y + height - radius);
            ctx.quadraticCurveTo(x + width, y + height, x + width - radius, y + height);
            ctx.lineTo(x + radius, y + height);
            ctx.quadraticCurveTo(x, y + height, x, y + height - radius);
            ctx.lineTo(x, y + radius);
            ctx.quadraticCurveTo(x, y, x + radius, y);
            ctx.closePath();
        }
    </script>
</body>
</html>
