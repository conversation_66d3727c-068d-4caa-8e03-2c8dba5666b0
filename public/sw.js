const CACHE_NAME = 'panda-resume-v1';
const urlsToCache = [
  '/',
  '/manifest.json',
  '/home/<USER>'
];

// 安装事件
self.addEventListener('install', (event) => {
  event.waitUntil(
    caches.open(CACHE_NAME)
      .then((cache) => {
        return cache.addAll(urlsToCache);
      })
  );
});

// 激活事件
self.addEventListener('activate', (event) => {
  event.waitUntil(
    caches.keys().then((cacheNames) => {
      return Promise.all(
        cacheNames.map((cacheName) => {
          if (cacheName !== CACHE_NAME) {
            return caches.delete(cacheName);
          }
        })
      );
    })
  );
});

// 拦截请求
self.addEventListener('fetch', (event) => {
  // 过滤掉不支持的请求协议
  if (!event.request.url.startsWith('http')) {
    return;
  }

  // 过滤掉浏览器扩展和其他不需要缓存的请求
  if (event.request.url.includes('chrome-extension') ||
      event.request.url.includes('moz-extension') ||
      event.request.url.includes('safari-extension') ||
      event.request.url.includes('clarity.ms') ||
      event.request.url.includes('bat.bing.com') ||
      event.request.url.includes('hm.baidu.com')) {
    return;
  }

  event.respondWith(
    caches.match(event.request)
      .then((response) => {
        // 如果缓存中有，直接返回
        if (response) {
          return response;
        }

        // 否则发起网络请求
        return fetch(event.request).then((response) => {
          // 检查是否是有效响应
          if (!response || response.status !== 200 || response.type !== 'basic') {
            return response;
          }

          // 只缓存同源请求
          if (event.request.url.startsWith(self.location.origin)) {
            // 克隆响应
            const responseToCache = response.clone();

            caches.open(CACHE_NAME)
              .then((cache) => {
                cache.put(event.request, responseToCache);
              });
          }

          return response;
        }).catch(() => {
          // 网络请求失败时，尝试从缓存返回
          return caches.match(event.request);
        });
      })
  );
});
