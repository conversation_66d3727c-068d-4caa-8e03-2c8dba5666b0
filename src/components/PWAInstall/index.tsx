'use client';

import React, { useState, useEffect } from 'react';
import { X, Download, Smartphone, Monitor } from 'lucide-react';
import { Button } from '@/components/ui/button';

interface BeforeInstallPromptEvent extends Event {
  readonly platforms: string[];
  readonly userChoice: Promise<{
    outcome: 'accepted' | 'dismissed';
    platform: string;
  }>;
  prompt(): Promise<void>;
}

interface PWAInstallProps {
  className?: string;
}

export default function PWAInstall({ className = '' }: PWAInstallProps) {
  const [deferredPrompt, setDeferredPrompt] = useState<BeforeInstallPromptEvent | null>(null);
  const [showInstallPrompt, setShowInstallPrompt] = useState(false);
  const [isIOS, setIsIOS] = useState(false);
  const [isStandalone, setIsStandalone] = useState(false);

  useEffect(() => {
    // 检测是否为 iOS
    const iOS = /iPad|iPhone|iPod/.test(navigator.userAgent);
    setIsIOS(iOS);

    // 检测是否已经是独立模式（已安装）
    const standalone = window.matchMedia('(display-mode: standalone)').matches;
    setIsStandalone(standalone);

    // 监听 beforeinstallprompt 事件
    const handleBeforeInstallPrompt = (e: Event) => {
      e.preventDefault();
      setDeferredPrompt(e as BeforeInstallPromptEvent);
      
      // 检查是否应该显示安装提示
      const lastDismissed = localStorage.getItem('pwa-install-dismissed');
      const now = Date.now();
      const oneWeek = 7 * 24 * 60 * 60 * 1000; // 一周
      
      if (!lastDismissed || now - parseInt(lastDismissed) > oneWeek) {
        setShowInstallPrompt(true);
      }
    };

    window.addEventListener('beforeinstallprompt', handleBeforeInstallPrompt);

    return () => {
      window.removeEventListener('beforeinstallprompt', handleBeforeInstallPrompt);
    };
  }, []);

  const handleInstallClick = async () => {
    if (!deferredPrompt) return;

    try {
      await deferredPrompt.prompt();
      const { outcome } = await deferredPrompt.userChoice;
      
      if (outcome === 'accepted') {
        console.log('用户接受了安装');
      } else {
        console.log('用户拒绝了安装');
      }
      
      setDeferredPrompt(null);
      setShowInstallPrompt(false);
    } catch (error) {
      console.error('安装过程中出错:', error);
    }
  };

  const handleDismiss = () => {
    setShowInstallPrompt(false);
    localStorage.setItem('pwa-install-dismissed', Date.now().toString());
  };

  // 如果已经是独立模式，不显示安装提示
  if (isStandalone) {
    return null;
  }

  // iOS 设备显示手动安装指引
  if (isIOS && showInstallPrompt) {
    return (
      <div className={`fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4 ${className}`}>
        <div className="bg-white rounded-lg p-6 max-w-sm w-full">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold">安装应用</h3>
            <button onClick={handleDismiss} className="text-gray-500 hover:text-gray-700">
              <X size={20} />
            </button>
          </div>
          
          <div className="flex items-center mb-4">
            <img src="/home/<USER>" alt="熊猫简历" className="w-12 h-12 rounded-lg mr-3" />
            <div>
              <div className="font-medium">熊猫简历</div>
              <div className="text-sm text-gray-500">pandaresume.com</div>
            </div>
          </div>

          <div className="text-sm text-gray-600 mb-4">
            <p className="mb-2">要安装此应用，请点击浏览器底部的分享按钮</p>
            <div className="flex items-center text-blue-600">
              <span className="mr-2">📤</span>
              <span>然后选择"添加到主屏幕"</span>
            </div>
          </div>

          <div className="flex gap-2">
            <Button variant="outline" onClick={handleDismiss} className="flex-1">
              取消
            </Button>
          </div>
        </div>
      </div>
    );
  }

  // Android/Chrome 显示安装按钮
  if (deferredPrompt && showInstallPrompt) {
    return (
      <div className={`fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4 ${className}`}>
        <div className="bg-white rounded-lg p-6 max-w-sm w-full">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold">安装应用</h3>
            <button onClick={handleDismiss} className="text-gray-500 hover:text-gray-700">
              <X size={20} />
            </button>
          </div>
          
          <div className="flex items-center mb-4">
            <img src="/home/<USER>" alt="熊猫简历" className="w-12 h-12 rounded-lg mr-3" />
            <div>
              <div className="font-medium">熊猫简历</div>
              <div className="text-sm text-gray-500">pandaresume.com</div>
            </div>
          </div>

          <div className="text-sm text-gray-600 mb-4">
            <p>安装应用到桌面，享受更好的使用体验：</p>
            <ul className="mt-2 space-y-1">
              <li className="flex items-center">
                <Monitor size={16} className="mr-2 text-blue-600" />
                <span>桌面快速访问</span>
              </li>
              <li className="flex items-center">
                <Smartphone size={16} className="mr-2 text-blue-600" />
                <span>离线使用</span>
              </li>
              <li className="flex items-center">
                <Download size={16} className="mr-2 text-blue-600" />
                <span>更快的加载速度</span>
              </li>
            </ul>
          </div>

          <div className="flex gap-2">
            <Button variant="outline" onClick={handleDismiss} className="flex-1">
              取消
            </Button>
            <Button onClick={handleInstallClick} className="flex-1 bg-primary hover:bg-purple-600">
              安装
            </Button>
          </div>
        </div>
      </div>
    );
  }

  return null;
}

// 安装到桌面的浮动按钮组件
export function PWAInstallButton({ className = '' }: PWAInstallProps) {
  const [deferredPrompt, setDeferredPrompt] = useState<BeforeInstallPromptEvent | null>(null);
  const [isStandalone, setIsStandalone] = useState(false);

  useEffect(() => {
    const standalone = window.matchMedia('(display-mode: standalone)').matches;
    setIsStandalone(standalone);

    const handleBeforeInstallPrompt = (e: Event) => {
      e.preventDefault();
      setDeferredPrompt(e as BeforeInstallPromptEvent);
    };

    window.addEventListener('beforeinstallprompt', handleBeforeInstallPrompt);

    return () => {
      window.removeEventListener('beforeinstallprompt', handleBeforeInstallPrompt);
    };
  }, []);

  const handleInstallClick = async () => {
    if (!deferredPrompt) return;

    try {
      await deferredPrompt.prompt();
      const { outcome } = await deferredPrompt.userChoice;
      
      if (outcome === 'accepted') {
        console.log('用户接受了安装');
      }
      
      setDeferredPrompt(null);
    } catch (error) {
      console.error('安装过程中出错:', error);
    }
  };

  // 如果已经是独立模式或没有安装提示，不显示按钮
  if (isStandalone || !deferredPrompt) {
    return null;
  }

  return (
    <Button
      onClick={handleInstallClick}
      className={`fixed bottom-20 right-4 bg-green-500 hover:bg-green-600 text-white rounded-full px-4 py-2 shadow-lg z-40 ${className}`}
    >
      <Download size={16} className="mr-2" />
      安装到桌面
    </Button>
  );
}
