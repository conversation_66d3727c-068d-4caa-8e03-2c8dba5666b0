'use client';

import React, { useState, useEffect } from 'react';
import { Download } from 'lucide-react';
import { usePathname } from 'next/navigation';

interface BeforeInstallPromptEvent extends Event {
  readonly platforms: string[];
  readonly userChoice: Promise<{
    outcome: 'accepted' | 'dismissed';
    platform: string;
  }>;
  prompt(): Promise<void>;
}

/**
 * 悬浮PWA安装按钮组件
 * 固定在右下角客服按钮上方
 */
export default function FloatingPWAInstall() {
  const [deferredPrompt, setDeferredPrompt] = useState<BeforeInstallPromptEvent | null>(null);
  const [isStandalone, setIsStandalone] = useState(false);
  const [showTooltip, setShowTooltip] = useState(false);
  const pathname = usePathname();

  // 需要隐藏安装按钮的页面路径（与客服按钮保持一致）
  const hiddenPaths = ['/make/gen/', '/make/print/'];

  useEffect(() => {
    // 检测是否已经是独立模式（已安装）
    const standalone = window.matchMedia('(display-mode: standalone)').matches;
    setIsStandalone(standalone);

    // 监听 beforeinstallprompt 事件
    const handleBeforeInstallPrompt = (e: Event) => {
      e.preventDefault();
      setDeferredPrompt(e as BeforeInstallPromptEvent);
    };

    window.addEventListener('beforeinstallprompt', handleBeforeInstallPrompt);

    return () => {
      window.removeEventListener('beforeinstallprompt', handleBeforeInstallPrompt);
    };
  }, []);

  const handleInstallClick = async () => {
    if (!deferredPrompt) return;

    try {
      await deferredPrompt.prompt();
      const { outcome } = await deferredPrompt.userChoice;
      
      if (outcome === 'accepted') {
        console.log('用户接受了安装');
      } else {
        console.log('用户拒绝了安装');
      }
      
      setDeferredPrompt(null);
    } catch (error) {
      console.error('安装过程中出错:', error);
    }
  };

  // 检查当前路径是否需要隐藏按钮
  const shouldHide = hiddenPaths.some(path => pathname?.includes(path));

  // 如果需要隐藏、已经是独立模式或没有安装提示，则不显示按钮
  if (shouldHide || isStandalone || !deferredPrompt) {
    return null;
  }

  return (
    <div className="fixed bottom-24 right-6 z-[9998]">
      {/* 脉冲动画效果 */}
      <div className="absolute inset-0 w-12 h-12 bg-green-500 rounded-full animate-ping opacity-20"></div>

      {/* 安装按钮容器 */}
      <div className="relative inline-block">
        <button
          className="w-12 h-12 bg-green-500 hover:bg-green-600 rounded-full shadow-lg hover:shadow-xl transition-all duration-300 flex items-center justify-center group relative z-10"
          title="安装应用"
          onClick={handleInstallClick}
          onMouseEnter={() => setShowTooltip(true)}
          onMouseLeave={() => setShowTooltip(false)}
        >
          {/* 下载图标 */}
          <Download className="w-6 h-6 text-white group-hover:scale-110 transition-transform duration-300" />
        </button>

        {/* 提示悬浮窗 */}
        {showTooltip && (
          <div
            className="absolute bottom-full right-0 mb-2 z-[9999] bg-white rounded-lg shadow-lg p-3 border border-gray-200 w-36"
            onMouseEnter={() => setShowTooltip(true)}
            onMouseLeave={() => setShowTooltip(false)}
          >
            <div className="text-center">
              <div className="text-sm text-gray-800 font-medium mb-1">安装到桌面</div>
              <div className="text-xs text-gray-500">更快访问，离线使用</div>
            </div>

            {/* 小箭头 */}
            <div className="absolute top-full right-4 w-0 h-0 border-l-[6px] border-r-[6px] border-t-[6px] border-l-transparent border-r-transparent border-t-white"></div>
          </div>
        )}
      </div>
    </div>
  );
}
