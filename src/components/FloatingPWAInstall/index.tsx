'use client';

import React, { useState, useEffect } from 'react';
import { Download } from 'lucide-react';
import { usePathname } from 'next/navigation';

interface BeforeInstallPromptEvent extends Event {
  readonly platforms: string[];
  readonly userChoice: Promise<{
    outcome: 'accepted' | 'dismissed';
    platform: string;
  }>;
  prompt(): Promise<void>;
}

/**
 * 悬浮PWA安装按钮组件
 * 固定在右下角客服按钮上方
 */
export default function FloatingPWAInstall() {
  const [deferredPrompt, setDeferredPrompt] = useState<BeforeInstallPromptEvent | null>(null);
  const [isStandalone, setIsStandalone] = useState(false);
  const [showTooltip, setShowTooltip] = useState(false);
  const [forceShow, setForceShow] = useState(false);
  const pathname = usePathname();

  // 需要隐藏安装按钮的页面路径（与客服按钮保持一致）
  const hiddenPaths = ['/make/gen/', '/make/print/'];

  useEffect(() => {
    // 检测是否已经是独立模式（已安装）
    const standalone = window.matchMedia('(display-mode: standalone)').matches;
    setIsStandalone(standalone);

    // 调试信息
    console.log('PWA Debug - 是否独立模式:', standalone);
    console.log('PWA Debug - 当前路径:', pathname);

    // 立即检查基本条件
    console.log('PWA Debug - 基本检查:', {
      protocol: window.location.protocol,
      hostname: window.location.hostname,
      serviceWorker: 'serviceWorker' in navigator,
      manifest: !!document.querySelector('link[rel="manifest"]'),
      userAgent: navigator.userAgent.substring(0, 50) + '...'
    });

    // 监听 beforeinstallprompt 事件
    const handleBeforeInstallPrompt = (e: Event) => {
      console.log('PWA Debug - beforeinstallprompt 事件触发！！！');
      e.preventDefault();
      setDeferredPrompt(e as BeforeInstallPromptEvent);
      setForceShow(false); // 有原生支持就不需要强制显示
    };

    window.addEventListener('beforeinstallprompt', handleBeforeInstallPrompt);

    // 检查是否已经有 beforeinstallprompt 事件在等待
    if ((window as any).deferredPrompt) {
      console.log('PWA Debug - 发现已存在的 deferredPrompt');
      setDeferredPrompt((window as any).deferredPrompt);
    }

    // 检查PWA安装条件
    const checkPWAConditions = async () => {
      const conditions = {
        protocol: window.location.protocol === 'https:' || window.location.hostname === 'localhost',
        serviceWorker: 'serviceWorker' in navigator,
        manifest: !!document.querySelector('link[rel="manifest"]'),
        standalone: !window.matchMedia('(display-mode: standalone)').matches,
        userAgent: navigator.userAgent.includes('Chrome') || navigator.userAgent.includes('Edge')
      };

      console.log('PWA Debug - 安装条件检查:', conditions);

      // 检查manifest是否可以正确加载
      try {
        const manifestResponse = await fetch('/manifest.json');
        const manifestData = await manifestResponse.json();
        console.log('PWA Debug - Manifest 内容:', manifestData);
      } catch (error) {
        console.error('PWA Debug - Manifest 加载失败:', error);
      }
    };

    // 检查Service Worker
    if ('serviceWorker' in navigator) {
      navigator.serviceWorker.ready.then(() => {
        console.log('PWA Debug - Service Worker 已就绪');

        // 立即执行条件检查
        setTimeout(() => {
          checkPWAConditions();
        }, 100);

        // 如果Service Worker就绪但没有beforeinstallprompt，延迟显示按钮
        setTimeout(() => {
          console.log('PWA Debug - 3秒后检查，deferredPrompt:', !!deferredPrompt, 'isStandalone:', isStandalone);
          if (!deferredPrompt && !isStandalone) {
            console.log('PWA Debug - 强制显示安装按钮');
            setForceShow(true);
          }
        }, 3000);
      });
    }

    return () => {
      window.removeEventListener('beforeinstallprompt', handleBeforeInstallPrompt);
    };
  }, [pathname]);

  const handleInstallClick = async () => {
    if (deferredPrompt) {
      try {
        await deferredPrompt.prompt();
        const { outcome } = await deferredPrompt.userChoice;

        if (outcome === 'accepted') {
          console.log('用户接受了安装');
        } else {
          console.log('用户拒绝了安装');
        }

        setDeferredPrompt(null);
        setForceShow(false);
      } catch (error) {
        console.error('安装过程中出错:', error);
      }
    } else {
      // 检测浏览器类型并提供相应的安装指引
      const isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent);
      const isEdge = navigator.userAgent.includes('Edge') || navigator.userAgent.includes('Edg/');
      const isChrome = navigator.userAgent.includes('Chrome') && !isEdge;
      const isSafari = navigator.userAgent.includes('Safari') && !isChrome && !isEdge;

      let message = '安装应用到桌面：\n\n';

      if (isIOS) {
        message += '📱 iOS Safari:\n1. 点击底部分享按钮 📤\n2. 选择"添加到主屏幕"\n3. 点击"添加"';
      } else if (isEdge) {
        message += '🔷 Microsoft Edge:\n1. 点击地址栏右侧的应用安装图标 ⊞\n2. 或点击菜单 ⋯ → "应用" → "将此站点安装为应用"\n3. 点击"安装"';
      } else if (isChrome) {
        message += '🟢 Google Chrome:\n1. 点击地址栏右侧的安装图标 ⊞\n2. 或点击菜单 ⋮ → "安装应用"\n3. 点击"安装"';
      } else if (isSafari) {
        message += '🔵 Safari:\n1. 点击菜单栏 → "文件" → "添加到程序坞"\n2. 或将页面添加为书签';
      } else {
        message += '🌐 其他浏览器:\n1. 查找浏览器菜单中的"安装"选项\n2. 或将此页面添加为书签\n3. 建议使用 Chrome 或 Edge 获得最佳体验';
      }

      message += '\n\n✨ 安装后可享受:\n• 桌面快速访问\n• 离线使用\n• 更快的加载速度';

      alert(message);
    }
  };

  // 检查当前路径是否需要隐藏按钮
  const shouldHide = hiddenPaths.some(path => pathname?.includes(path));

  // 调试信息
  console.log('PWA Debug - shouldHide:', shouldHide);
  console.log('PWA Debug - isStandalone:', isStandalone);
  console.log('PWA Debug - deferredPrompt:', !!deferredPrompt);
  console.log('PWA Debug - forceShow:', forceShow);

  // 临时显示按钮用于测试（移除 !deferredPrompt 条件）
  if (shouldHide || isStandalone) {
    return null;
  }

  // 如果有deferredPrompt或者强制显示，就显示按钮
  if (!deferredPrompt && !forceShow) {
    return null;
  }



  // 确定按钮颜色和状态
  const isEdge = navigator.userAgent.includes('Edge') || navigator.userAgent.includes('Edg/');
  const isChrome = navigator.userAgent.includes('Chrome') && !isEdge;

  // 如果是支持PWA的浏览器，使用绿色表示可以安装
  const canInstall = deferredPrompt || isEdge || isChrome;
  const buttonColor = canInstall ? 'bg-green-500 hover:bg-green-600' : 'bg-blue-500 hover:bg-blue-600';
  const buttonTitle = canInstall ? '安装应用到桌面' : '查看安装方法';

  console.log('PWA Debug - 渲染按钮，buttonColor:', buttonColor, 'buttonTitle:', buttonTitle);

  return (
    <div className="fixed bottom-24 right-6 z-[9999]" style={{
      position: 'fixed',
      bottom: '96px',
      right: '24px',
      zIndex: 9999,
      pointerEvents: 'auto'
    }}>
      {/* 脉冲动画效果 */}
      <div className={`absolute inset-0 w-12 h-12 ${canInstall ? 'bg-green-500' : 'bg-blue-500'} rounded-full animate-ping opacity-20`}></div>

      {/* 安装按钮容器 */}
      <div className="relative inline-block">
        <button
          className={`w-12 h-12 ${buttonColor} rounded-full shadow-lg hover:shadow-xl transition-all duration-300 flex items-center justify-center group relative z-10`}
          title={buttonTitle}
          onClick={handleInstallClick}
          onMouseEnter={() => setShowTooltip(true)}
          onMouseLeave={() => setShowTooltip(false)}
          style={{
            width: '48px',
            height: '48px',
            backgroundColor: canInstall ? '#10b981' : '#3b82f6',
            borderRadius: '50%',
            border: 'none',
            cursor: 'pointer'
          }}
        >
          {/* 下载图标 */}
          <Download className="w-6 h-6 text-white group-hover:scale-110 transition-transform duration-300" />
        </button>

        {/* 提示悬浮窗 */}
        {showTooltip && (
          <div
            className="absolute bottom-full right-0 mb-2 z-[9999] bg-white rounded-lg shadow-lg p-3 border border-gray-200 w-36"
            onMouseEnter={() => setShowTooltip(true)}
            onMouseLeave={() => setShowTooltip(false)}
          >
            <div className="text-center">
              <div className="text-sm text-gray-800 font-medium mb-1">
                {canInstall ? '安装到桌面' : '安装指引'}
              </div>
              <div className="text-xs text-gray-500">
                {canInstall ? '更快访问，离线使用' : '查看安装方法'}
              </div>
            </div>

            {/* 小箭头 */}
            <div className="absolute top-full right-4 w-0 h-0 border-l-[6px] border-r-[6px] border-t-[6px] border-l-transparent border-r-transparent border-t-white"></div>
          </div>
        )}
      </div>
    </div>
  );
}
