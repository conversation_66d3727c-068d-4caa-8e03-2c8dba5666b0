'use client';

import React, { useState, useEffect } from 'react';
import { Download } from 'lucide-react';
import { usePathname } from 'next/navigation';

interface BeforeInstallPromptEvent extends Event {
  readonly platforms: string[];
  readonly userChoice: Promise<{
    outcome: 'accepted' | 'dismissed';
    platform: string;
  }>;
  prompt(): Promise<void>;
}

/**
 * 悬浮PWA安装按钮组件
 * 固定在右下角客服按钮上方
 */
export default function FloatingPWAInstall() {
  const [deferredPrompt, setDeferredPrompt] = useState<BeforeInstallPromptEvent | null>(null);
  const [isStandalone, setIsStandalone] = useState(false);
  const [showTooltip, setShowTooltip] = useState(false);
  const [forceShow, setForceShow] = useState(false);
  const pathname = usePathname();

  // 需要隐藏安装按钮的页面路径（与客服按钮保持一致）
  const hiddenPaths = ['/make/gen/', '/make/print/'];

  useEffect(() => {
    // 检测是否已经是独立模式（已安装）
    const standalone = window.matchMedia('(display-mode: standalone)').matches;
    setIsStandalone(standalone);

    // 调试信息
    console.log('PWA Debug - 是否独立模式:', standalone);
    console.log('PWA Debug - 当前路径:', pathname);

    // 监听 beforeinstallprompt 事件
    const handleBeforeInstallPrompt = (e: Event) => {
      console.log('PWA Debug - beforeinstallprompt 事件触发');
      e.preventDefault();
      setDeferredPrompt(e as BeforeInstallPromptEvent);
    };

    window.addEventListener('beforeinstallprompt', handleBeforeInstallPrompt);

    // 检查Service Worker
    if ('serviceWorker' in navigator) {
      navigator.serviceWorker.ready.then(() => {
        console.log('PWA Debug - Service Worker 已就绪');
        // 如果Service Worker就绪但没有beforeinstallprompt，延迟显示按钮
        setTimeout(() => {
          console.log('PWA Debug - 3秒后检查，deferredPrompt:', !!deferredPrompt, 'isStandalone:', isStandalone);
          if (!deferredPrompt && !isStandalone) {
            console.log('PWA Debug - 强制显示安装按钮');
            setForceShow(true);
          }
        }, 3000);
      });
    }

    return () => {
      window.removeEventListener('beforeinstallprompt', handleBeforeInstallPrompt);
    };
  }, [pathname]);

  const handleInstallClick = async () => {
    if (deferredPrompt) {
      try {
        await deferredPrompt.prompt();
        const { outcome } = await deferredPrompt.userChoice;

        if (outcome === 'accepted') {
          console.log('用户接受了安装');
        } else {
          console.log('用户拒绝了安装');
        }

        setDeferredPrompt(null);
        setForceShow(false);
      } catch (error) {
        console.error('安装过程中出错:', error);
      }
    } else {
      // 没有原生支持，显示手动安装指引
      const isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent);
      const isChrome = navigator.userAgent.includes('Chrome');

      let message = '手动安装应用：\n\n';

      if (isIOS) {
        message += '1. 点击浏览器底部的分享按钮 📤\n2. 选择"添加到主屏幕"\n3. 点击"添加"';
      } else if (isChrome) {
        message += '1. 点击地址栏右侧的安装图标\n2. 或者在浏览器菜单中选择"安装应用"';
      } else {
        message += '1. 在浏览器菜单中查找"安装"或"添加到主屏幕"选项\n2. 或者将此页面添加为书签';
      }

      alert(message);
    }
  };

  // 检查当前路径是否需要隐藏按钮
  const shouldHide = hiddenPaths.some(path => pathname?.includes(path));

  // 调试信息
  console.log('PWA Debug - shouldHide:', shouldHide);
  console.log('PWA Debug - isStandalone:', isStandalone);
  console.log('PWA Debug - deferredPrompt:', !!deferredPrompt);
  console.log('PWA Debug - forceShow:', forceShow);

  // 临时显示按钮用于测试（移除 !deferredPrompt 条件）
  if (shouldHide || isStandalone) {
    return null;
  }

  // 如果有deferredPrompt或者强制显示，就显示按钮
  if (!deferredPrompt && !forceShow) {
    return null;
  }



  // 确定按钮颜色和状态
  const buttonColor = deferredPrompt ? 'bg-green-500 hover:bg-green-600' : 'bg-blue-500 hover:bg-blue-600';
  const buttonTitle = deferredPrompt ? '安装应用' : '安装指引';

  console.log('PWA Debug - 渲染按钮，buttonColor:', buttonColor, 'buttonTitle:', buttonTitle);

  return (
    <div className="fixed bottom-24 right-6 z-[9999]" style={{
      position: 'fixed',
      bottom: '96px',
      right: '24px',
      zIndex: 9999,
      pointerEvents: 'auto'
    }}>
      {/* 脉冲动画效果 */}
      <div className={`absolute inset-0 w-12 h-12 ${deferredPrompt ? 'bg-green-500' : 'bg-blue-500'} rounded-full animate-ping opacity-20`}></div>

      {/* 安装按钮容器 */}
      <div className="relative inline-block">
        <button
          className={`w-12 h-12 ${buttonColor} rounded-full shadow-lg hover:shadow-xl transition-all duration-300 flex items-center justify-center group relative z-10`}
          title={buttonTitle}
          onClick={handleInstallClick}
          onMouseEnter={() => setShowTooltip(true)}
          onMouseLeave={() => setShowTooltip(false)}
          style={{
            width: '48px',
            height: '48px',
            backgroundColor: deferredPrompt ? '#10b981' : '#3b82f6',
            borderRadius: '50%',
            border: 'none',
            cursor: 'pointer'
          }}
        >
          {/* 下载图标 */}
          <Download className="w-6 h-6 text-white group-hover:scale-110 transition-transform duration-300" />
        </button>

        {/* 提示悬浮窗 */}
        {showTooltip && (
          <div
            className="absolute bottom-full right-0 mb-2 z-[9999] bg-white rounded-lg shadow-lg p-3 border border-gray-200 w-36"
            onMouseEnter={() => setShowTooltip(true)}
            onMouseLeave={() => setShowTooltip(false)}
          >
            <div className="text-center">
              <div className="text-sm text-gray-800 font-medium mb-1">
                {deferredPrompt ? '安装到桌面' : '安装指引'}
              </div>
              <div className="text-xs text-gray-500">
                {deferredPrompt ? '更快访问，离线使用' : '查看安装方法'}
              </div>
            </div>

            {/* 小箭头 */}
            <div className="absolute top-full right-4 w-0 h-0 border-l-[6px] border-r-[6px] border-t-[6px] border-l-transparent border-r-transparent border-t-white"></div>
          </div>
        )}
      </div>
    </div>
  );
}
