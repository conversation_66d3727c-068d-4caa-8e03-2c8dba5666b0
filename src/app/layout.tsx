import type { Metadata } from "next";
import "./globals.css";
import { Providers } from "@/components/Providers";
import BdVidTracker from "@/components/BdVidTracker";
import ChannelTracker from "@/components/ChannelTracker";
import FloatingCustomerService from "@/components/FloatingCustomerService";
import PWARegister from "@/components/PWARegister";
import FloatingP<PERSON>Install from "@/components/FloatingPWAInstall";
import Script from "next/script";

export const metadata: Metadata = {
  title: '熊猫简历-简历模板免费使用-AI简历生成-个人简历模板在线制作工具',
  keywords: ['简历模板', '简历制作', '简历模板免费使用', '个人简历', '个人简历模板'],
  description: '熊猫简历PandaResume专业在线简历制作；海量个人简历模板免费使用，简历模板下载，AI简历一键生成，AI辅助优化简历内容，仅需5分钟即可拥有一份精美优秀的个人简历！秒变满分候选人，让心动offer拿到手软。',
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="zh-CN">
      <head>
        {/* PWA Manifest */}
        <link rel="manifest" href="/manifest.json" />
        <meta name="theme-color" content="#824dfc" />
        <meta name="apple-mobile-web-app-capable" content="yes" />
        <meta name="apple-mobile-web-app-status-bar-style" content="default" />
        <meta name="apple-mobile-web-app-title" content="熊猫简历" />
        <link rel="apple-touch-icon" href="/icons/icon-192x192.png" />

        <Script
          id='bing-uet'
          strategy="afterInteractive"
        >{`(function(w,d,t,r,u){var f,n,i;w[u]=w[u]||[],f=function(){var o={ti:"187204870", enableAutoSpaTracking: true};o.q=w[u],w[u]=new UET(o),w[u].push("pageLoad")},n=d.createElement(t),n.src=r,n.async=1,n.onload=n.onreadystatechange=function(){var s=this.readyState;s&&s!=="loaded"&&s!=="complete"||(f(),n.onload=n.onreadystatechange=null)},i=d.getElementsByTagName(t)[0],i.parentNode.insertBefore(n,i)})(window,document,"script","//bat.bing.com/bat.js","uetq");`}</Script>
        <Script
          id='baidu'
          strategy="afterInteractive"
        >{`var _hmt = _hmt || [];
(function() {
  var hm = document.createElement("script");
  hm.src = "https://hm.baidu.com/hm.js?52449a38eaa91c4cf33d5783cea1db47";
  var s = document.getElementsByTagName("script")[0];
  s.parentNode.insertBefore(hm, s);
})();
`}</Script>
        <Script
          defer
          src="https://data.pandaresume.cn/script.js"
          data-website-id="0440f1ce-d4c3-4a31-a7ef-cbfbab8bddd0"
          strategy="afterInteractive"
        />
      </head>

      <body
        className={`antialiased`}
      >
        <Providers>
          <PWARegister />
          <BdVidTracker />
          <ChannelTracker />
          {children}
          <FloatingCustomerService />
          <FloatingPWAInstall />
        </Providers>

      </body>
    </html>
  );
}
